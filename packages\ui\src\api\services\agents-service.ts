import {
  Agent,
  CreateAgentType,
  UpdateAgentType,
  agentSchema,
} from '../../models/agent/agent-model';
import { ApiLibrary, ApiLibraryConfig, appQueryClient } from '../api-library';
import { ApiLibraryHelper } from '../api-library-helpers';

export default class AgentsService extends ApiLibraryHelper {
  protected schema = agentSchema;
  protected path = '/agents';
  protected serviceName = 'agents' as keyof ApiLibrary;

  create({
    data,
    projectId,
    config,
  }: {
    data: CreateAgentType;
    projectId: string;
    config?: ApiLibraryConfig;
  }) {
    return super.apiFetch<Agent>({
      httpMethod: 'post',
      mockConfig: {
        schema: agentSchema,
      },
      path: `/projects/${projectId}/agents`,
      data,
      onSuccess: async () => {
        await Promise.all([
          appQueryClient.invalidateQueries({
            queryKey: [this.serviceName, 'getList'],
          }),
        ]);
      },
      config,
    });
  }

  getList(args?: { config?: ApiLibraryConfig }) {
    return super._getList<Agent[]>({
      config: {
        ...args?.config,
        params: {
          expansion: [
            'createdAt',
            'updatedAt',
            'profileImageUrl',
            'description',
            'project',
            'connections',
            'knowledge',
            'triggerIds',
            'toolIds',
          ],
          ...args?.config?.params,
        },
      },
    });
  }

  getById({ id, config }: { id: string; config?: ApiLibraryConfig }) {
    return super.apiFetch<Agent>({
      httpMethod: 'get',
      mockConfig: {
        schema: agentSchema,
      },
      path: `${this.path}/${id}`,
      config: {
        ...config,
        params: {
          expansion: [
            'createdAt',
            'updatedAt',
            'profileImageUrl',
            'description',
            'project',
            'instructions',
            'maxRetries',
            'frequencyPenalty',
            'maxTokens',
            'maxToolRoundtrips',
            'presencePenalty',
            'seed',
            'temperature',
            'topP',
            'tools',
            'triggers',
            'connections',
            'actions',
            'knowledge',
            'variables',
            'workflows',
            'subAgents',
            'webAccess',
            'phoneAccess',
            'llmConnection',
            'llmModel',
            'llmProvider',
            'triggerIds',
            'toolIds',
            'taskNamingInstructions',
            'taskNamingLlmConnection',
            'taskNamingLlmModel',
            'taskNamingLlmProvider',
          ],
          ...config?.params,
        },
      },
    });
  }

  update({
    id,
    data,
    config,
  }: {
    id: string;
    data: UpdateAgentType;
    config?: ApiLibraryConfig;
  }) {
    return super._update<Agent>({
      id,
      data,
      config,
    });
  }

  getPresignedPostUrlForProfileImage({
    id,
    fileName,
    config,
  }: {
    id: string;
    fileName: string;
    config?: ApiLibraryConfig;
  }) {
    return super.apiFetch<{
      presignedPostData: { url: string; fields: Record<string, string> };
      pathUrl: string;
    }>({
      httpMethod: 'post',
      path: `${this.path}/${id}/profile-image-post-url`,
      data: { fileName },
      config,
      mockConfig: {
        isArray: false,
        schema: null,
      },
    });
  }

  delete({ id, config }: { id: string; config?: ApiLibraryConfig }) {
    return super._delete<boolean>({
      id,
      config,
    });
  }

  // API Key Management Methods
  generateApiKey({
    agentId,
    data,
    config,
  }: {
    agentId: string;
    data: {
      name: string;
      description?: string;
      expiresAt?: string;
      permissions?: Record<string, any>;
    };
    config?: ApiLibraryConfig;
  }) {
    return super.apiFetch<{
      id: string;
      name: string;
      description?: string;
      keyPreview: string;
      apiKey: string; // Only returned on creation
      createdAt: string;
      expiresAt?: string;
      permissions?: Record<string, any>;
    }>({
      httpMethod: 'post',
      path: `${this.path}/${agentId}/api-keys`,
      data,
      config,
      mockConfig: {
        schema: null,
      },
      onSuccess: async () => {
        await appQueryClient.invalidateQueries({
          queryKey: [this.serviceName, 'getApiKeys', agentId],
        });
      },
    });
  }

  getApiKeys({
    agentId,
    config,
  }: {
    agentId: string;
    config?: ApiLibraryConfig;
  }) {
    return super.apiFetch<Array<{
      id: string;
      name: string;
      description?: string;
      keyPreview: string;
      lastUsedAt?: string;
      createdAt: string;
      expiresAt?: string;
      isActive: boolean;
      permissions?: Record<string, any>;
    }>>({
      httpMethod: 'get',
      path: `${this.path}/${agentId}/api-keys`,
      config,
      mockConfig: {
        isArray: true,
        schema: null,
      },
    });
  }

  updateApiKey({
    agentId,
    apiKeyId,
    data,
    config,
  }: {
    agentId: string;
    apiKeyId: string;
    data: { isActive?: boolean; name?: string; description?: string };
    config?: ApiLibraryConfig;
  }) {
    return super.apiFetch<any>({
      httpMethod: 'patch',
      path: `${this.path}/${agentId}/api-keys/${apiKeyId}`,
      data,
      config,
      mockConfig: {
        schema: null,
      },
      onSuccess: async () => {
        await appQueryClient.invalidateQueries({
          queryKey: [this.serviceName, 'getApiKeys', agentId],
        });
      },
    });
  }

  deleteApiKey({
    agentId,
    apiKeyId,
    config,
  }: {
    agentId: string;
    apiKeyId: string;
    config?: ApiLibraryConfig;
  }) {
    return super.apiFetch<boolean>({
      httpMethod: 'delete',
      path: `${this.path}/${agentId}/api-keys/${apiKeyId}`,
      config,
      mockConfig: {
        schema: null,
      },
      onSuccess: async () => {
        await appQueryClient.invalidateQueries({
          queryKey: [this.serviceName, 'getApiKeys', agentId],
        });
      },
    });
  }
}
