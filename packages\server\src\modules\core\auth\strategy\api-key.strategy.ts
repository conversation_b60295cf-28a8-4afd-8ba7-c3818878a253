import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';

import { Request } from '../../../../types/request.type';
import { AgentApiKeysService } from '../../agents/agent-api-keys.service';

@Injectable()
export class ApiKeyStrategy extends PassportStrategy(Strategy, 'api-key') {
  constructor(private agentApiKeysService: AgentApiKeysService) {
    super();
  }

  async validate(req: any): Promise<Request['user']> {
    const apiKey = this.extractApiKey(req);
    
    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    try {
      const apiKeyContext = await this.agentApiKeysService.validateApiKey(apiKey);
      
      // Return a user context similar to JWT but for API key authentication
      const requestUser: Request['user'] = {
        email: 'api-key-user', // Placeholder since API keys don't have user emails
        userId: 'api-key-user', // Placeholder since API keys don't have user IDs
        workspaceId: apiKeyContext.workspaceId,
        workspaceUserId: 'api-key-user', // Placeholder
        roles: ['MAINTAINER'], // API keys get maintainer access to their agent
        // Add API key specific context
        apiKeyId: apiKeyContext.apiKeyId,
        agentId: apiKeyContext.agentId,
        permissions: apiKeyContext.permissions,
        isApiKeyAuth: true,
      };

      return requestUser;
    } catch (error) {
      throw new UnauthorizedException('Invalid API key');
    }
  }

  private extractApiKey(req: any): string | null {
    // Check for API key in different locations
    // 1. Authorization header with Bearer token
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 2. X-API-Key header
    const apiKeyHeader = req.headers['x-api-key'];
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // 3. Query parameter
    const apiKeyQuery = req.query.api_key;
    if (apiKeyQuery) {
      return apiKeyQuery;
    }

    return null;
  }
}
