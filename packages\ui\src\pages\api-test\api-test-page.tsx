import { useState } from 'react';

import { Icons } from '../../components/icons';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { useToast } from '../../hooks/useToast';

export function ApiTestPage() {
  const { toast } = useToast();
  const [apiKey, setApiKey] = useState('');
  const [agentId, setAgentId] = useState('');
  const [message, setMessage] = useState('Hello! Can you help me?');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);

  const handleTest = async () => {
    if (!apiKey.trim()) {
      toast({ title: 'API Key is required', variant: 'destructive' });
      return;
    }
    if (!agentId.trim()) {
      toast({ title: 'Agent ID is required', variant: 'destructive' });
      return;
    }
    if (!message.trim()) {
      toast({ title: 'Message is required', variant: 'destructive' });
      return;
    }

    setLoading(true);
    setResponse('');

    try {
      const response = await fetch(`/api/v1/agents/${agentId}/chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          stream: false,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorData}`);
      }

      const data = await response.json();
      setResponse(JSON.stringify(data, null, 2));
      toast({ title: 'API test successful!' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setResponse(`Error: ${errorMessage}`);
      toast({ 
        title: 'API test failed', 
        description: errorMessage,
        variant: 'destructive' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopyResponse = async () => {
    if (response) {
      try {
        await navigator.clipboard.writeText(response);
        toast({ title: 'Response copied to clipboard' });
      } catch (error) {
        toast({ title: 'Failed to copy response', variant: 'destructive' });
      }
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">API Test Page</h1>
        <p className="text-muted-foreground">
          Test your agent API keys and endpoints here.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Test Form */}
        <Card>
          <Card.Header>
            <Card.Title>Test Agent API</Card.Title>
            <Card.Description>
              Enter your API key and agent details to test the chat endpoint.
            </Card.Description>
          </Card.Header>
          <Card.Content className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                type="password"
                placeholder="lecca_..."
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="agentId">Agent ID</Label>
              <Input
                id="agentId"
                placeholder="agent-uuid-here"
                value={agentId}
                onChange={(e) => setAgentId(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                You can find the Agent ID in the URL when viewing an agent
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Test Message</Label>
              <Textarea
                id="message"
                placeholder="Enter your test message..."
                rows={3}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>

            <Button 
              onClick={handleTest} 
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <Icons.spinner className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Icons.play className="w-4 h-4 mr-2" />
                  Test API
                </>
              )}
            </Button>
          </Card.Content>
        </Card>

        {/* Response */}
        <Card>
          <Card.Header>
            <div className="flex items-center justify-between">
              <div>
                <Card.Title>API Response</Card.Title>
                <Card.Description>
                  The response from your agent API call.
                </Card.Description>
              </div>
              {response && (
                <Button variant="outline" size="sm" onClick={handleCopyResponse}>
                  <Icons.copy className="w-4 h-4 mr-2" />
                  Copy
                </Button>
              )}
            </div>
          </Card.Header>
          <Card.Content>
            {response ? (
              <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto max-h-96">
                {response}
              </pre>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <Icons.messageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No response yet. Test your API to see results here.</p>
              </div>
            )}
          </Card.Content>
        </Card>
      </div>

      {/* CLI Examples */}
      <Card>
        <Card.Header>
          <Card.Title>CLI Testing Examples</Card.Title>
          <Card.Description>
            Use these commands to test your API from the command line.
          </Card.Description>
        </Card.Header>
        <Card.Content className="space-y-4">
          <div className="space-y-2">
            <Label>Basic Chat Request</Label>
            <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto">
{`curl -X POST "http://localhost:3000/api/v1/agents/YOUR_AGENT_ID/chat" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "Hello! Can you help me?",
    "stream": false
  }'`}
            </pre>
          </div>

          <div className="space-y-2">
            <Label>Streaming Chat Request</Label>
            <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto">
{`curl -X POST "http://localhost:3000/api/v1/agents/YOUR_AGENT_ID/chat" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "Tell me a story",
    "stream": true
  }'`}
            </pre>
          </div>

          <div className="space-y-2">
            <Label>Get Agent Info</Label>
            <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto">
{`curl -X GET "http://localhost:3000/api/v1/agents/YOUR_AGENT_ID" \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
            </pre>
          </div>

          <div className="space-y-2">
            <Label>PowerShell Example (Windows)</Label>
            <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto">
{`$headers = @{
    "Authorization" = "Bearer YOUR_API_KEY"
    "Content-Type" = "application/json"
}
$body = @{
    message = "Hello! Can you help me?"
    stream = $false
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3000/api/v1/agents/YOUR_AGENT_ID/chat" \\
  -Method POST -Headers $headers -Body $body`}
            </pre>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}
