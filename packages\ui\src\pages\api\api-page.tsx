import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import useApiQuery from '../../api/use-api-query';
import { EmptyPlaceholder } from '../../components/empty-placeholder';
import { Icons } from '../../components/icons';
import PageLayout from '../../components/layouts/page-layout';
import { GridLoader } from '../../components/loaders/grid-loader';
import { PageLoader } from '../../components/loaders/page-loader';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Agent } from '../../models/agent/agent-model';
import { AgentApiKey } from '../../models/agent-api-key-model';
import { toLocaleDateStringOrUndefined } from '../../utils/dates';

export function ApiPage() {
  const [search, setSearch] = useState('');
  const navigate = useNavigate();

  // Get all agents
  const { data: agents, isLoading: isLoadingAgents } = useApiQuery({
    service: 'agents',
    method: 'getList',
    apiLibraryArgs: {},
  });

  // Filter agents that have API keys (we'll show all for now)
  const agentsWithApiKeys = agents || [];

  if (isLoadingAgents) {
    return (
      <PageLoader>
        <GridLoader
          itemClassName="h-36"
          className="grid gap-6 grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3"
        />
      </PageLoader>
    );
  }

  const filteredAgents = agentsWithApiKeys.filter(
    (agent) =>
      agent.name.toLowerCase().includes(search.toLowerCase()) ||
      (agent.description &&
        agent.description.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <PageLayout
      title="API Management"
      subtitle="Manage API keys and external access for your agents"
      actions={[
        <Button
          variant="outline"
          onClick={() => navigate('/agents')}
        >
          <Icons.agent className="w-4 h-4 mr-2" />
          View All Agents
        </Button>
      ]}
      className="space-y-6"
    >
      {/* API Stats Summary */}
      <ApiStatsSummary agents={agentsWithApiKeys} />

      <div className="flex items-center justify-between">
        <Input
          type="search"
          placeholder="Search agents..."
          className="py-2 w-[200px] lg:w-[250px]"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
        
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Active API Keys</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
            <span>No API Keys</span>
          </div>
        </div>
      </div>

      {filteredAgents.length > 0 ? (
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3">
          {filteredAgents.map((agent) => (
            <AgentApiCard key={agent.id} agent={agent} />
          ))}
        </div>
      ) : (
        <EmptyPlaceholder
          icon={<Icons.key />}
          title="No Agents Found"
          description="No agents match your search criteria."
          buttonLabel="View All Agents"
          onClick={() => navigate('/agents')}
        />
      )}
    </PageLayout>
  );
}

function AgentApiCard({ agent }: { agent: Agent }) {
  const navigate = useNavigate();

  // Fetch API keys for this specific agent
  const { data: apiKeys, isLoading } = useApiQuery({
    service: 'agents',
    method: 'getApiKeys',
    apiLibraryArgs: {
      agentId: agent.id,
    },
  });

  const activeApiKeys = apiKeys?.filter(key => key.isActive) || [];
  const hasApiKeys = apiKeys && apiKeys.length > 0;

  const handleManageApiKeys = () => {
    navigate(`/projects/${agent.project?.id}/agents/${agent.id}/configure?tab=api-keys`);
  };

  const handleViewAgent = () => {
    navigate(`/projects/${agent.project?.id}/agents/${agent.id}`);
  };

  return (
    <Card className="relative">
      <Card.Header>
        <Card.Title className="flex justify-between items-start">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{agent.name}</span>
              {hasApiKeys ? (
                <div className="w-3 h-3 bg-green-500 rounded-full" title="Has API keys" />
              ) : (
                <div className="w-3 h-3 bg-gray-300 rounded-full" title="No API keys" />
              )}
            </div>
            <span className="line-clamp-2 text-sm font-normal text-muted-foreground">
              {agent.description || 'No description'}
            </span>
          </div>
        </Card.Title>
      </Card.Header>
      
      <Card.Content className="space-y-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Project:</span>
          <Badge variant="outline">{agent.project?.name}</Badge>
        </div>
        
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <Icons.spinner className="w-4 h-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading API keys...</span>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total API Keys:</span>
              <span className="font-medium">{apiKeys?.length || 0}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Active Keys:</span>
              <span className="font-medium text-green-600">{activeApiKeys.length}</span>
            </div>
            {apiKeys && apiKeys.length > 0 && (
              <div className="text-xs text-muted-foreground">
                Last used: {
                  apiKeys.find(key => key.lastUsedAt)?.lastUsedAt 
                    ? toLocaleDateStringOrUndefined(
                        apiKeys
                          .filter(key => key.lastUsedAt)
                          .sort((a, b) => new Date(b.lastUsedAt!).getTime() - new Date(a.lastUsedAt!).getTime())[0]
                          ?.lastUsedAt
                      )
                    : 'Never'
                }
              </div>
            )}
          </div>
        )}
      </Card.Content>
      
      <Card.Footer className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewAgent}
        >
          <Icons.eye className="w-4 h-4 mr-2" />
          View Agent
        </Button>
        <Button
          variant="default"
          size="sm"
          onClick={handleManageApiKeys}
        >
          <Icons.key className="w-4 h-4 mr-2" />
          {hasApiKeys ? 'Manage Keys' : 'Generate Key'}
        </Button>
      </Card.Footer>
    </Card>
  );
}

function ApiStatsSummary({ agents }: { agents: Agent[] }) {
  // This is a placeholder for future API usage statistics
  // You can later add charts and detailed analytics here

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">Total Agents</Card.Title>
          <Icons.agent className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">{agents.length}</div>
          <p className="text-xs text-muted-foreground">
            Agents in your workspace
          </p>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">API Enabled</Card.Title>
          <Icons.key className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">-</div>
          <p className="text-xs text-muted-foreground">
            Agents with API keys
          </p>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">Total API Keys</Card.Title>
          <Icons.key className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">-</div>
          <p className="text-xs text-muted-foreground">
            Active API keys
          </p>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">API Calls</Card.Title>
          <Icons.activity className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">-</div>
          <p className="text-xs text-muted-foreground">
            This month
          </p>
        </Card.Content>
      </Card>
    </div>
  );
}
