import { useEffect, useMemo, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';

import useApiQuery from '../../../api/use-api-query';
import { Icons } from '../../../components/icons';
import { UserSettings } from '../../../components/layouts/application-side-nav';
import { Loader } from '../../../components/loaders/loader';
import { Button } from '../../../components/ui/button';
import { Dialog } from '../../../components/ui/dialog';
import { Tooltip } from '../../../components/ui/tooltip';
import { useOnborda } from '../../../components/onboarda/OnbordaContext';
import { useUser } from '../../../hooks/useUser';
import { FormattedTaskMessage } from '../../../models/task/formatted-task-message-model';
import { NavAgentSelector } from '../../projects/components/nav-selectors/nav-agent-selector';



import { Chat } from './components/chat';
import { formatSavedMessagesToStreamedMessageFormat } from './utils/format-saved-messages-to-streamed-message-format';
import { ApiKeyModal } from './components/api-key-modal';

export function AgentChatPage() {
  const { workspaceUser } = useUser();
  const { projectId, agentId, taskId } = useParams();
  const navigate = useNavigate();
  const { startOnborda } = useOnborda();
  const [alreadyShowedAgentOverview, setAlreadyShowedAgentOverview] =
    useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);

  const { data: agent, isLoading: isLoadingAgent } = useApiQuery({
    service: 'agents',
    method: 'getById',
    apiLibraryArgs: {
      id: agentId!,
    },
  });
  const { data: task, isLoading: isLoadingTask } = useApiQuery({
    service: 'tasks',
    method: 'getById',
    apiLibraryArgs: {
      id: taskId ?? '', //Might not exist and that's okay
    },
  });

  const formattedTaskMessage = useMemo(() => {
    if (task?.messages) {
      return formatSavedMessagesToStreamedMessageFormat({
        messages: task.messages,
        currentAgentId: agentId!,
      });
    }
    return null;
  }, [agentId, task?.messages]);

  useEffect(() => {
    setTimeout(() => {
      if (
        !alreadyShowedAgentOverview &&
        window.innerWidth > 700 &&
        !workspaceUser?.user?.toursCompleted?.includes('agents-overview') &&
        !isLoadingAgent
      ) {
        setAlreadyShowedAgentOverview(true);
        startOnborda('agents-overview');
      }
    }, 500);
  }, [
    alreadyShowedAgentOverview,
    isLoadingAgent,
    startOnborda,
    workspaceUser?.user?.toursCompleted,
  ]);

  if ((isLoadingAgent || isLoadingTask) && !formattedTaskMessage?.length) {
    return <Loader />;
  }

  if (!agent) {
    return <div>Agent not found</div>;
  }

  return (
    <div>
      <nav className="w-full flex items-center justify-between py-1">
        <div>
          <div className="mx-2 sm:mx-4 flex items-center space-x-1">
            <Link to={`/agents/${agentId}`}>
              <span>{agent.name}</span>
            </Link>
            <NavAgentSelector />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Tooltip>
            <Tooltip.Trigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowApiKeyModal(true)}
              >
                <Icons.key className="w-4 h-4" />
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content>
              Generate API Key
            </Tooltip.Content>
          </Tooltip>
          <div className="hidden sm:block">
            <UserSettings isCollapsed={false} className="size-8" />
          </div>
        </div>
      </nav>
      <Chat
        agent={agent}
        taskId={taskId}
        projectId={projectId!}
        defaultMessages={
          formattedTaskMessage as unknown as FormattedTaskMessage[]
        }
      />

      {/* API Key Modal */}
      <ApiKeyModal
        open={showApiKeyModal}
        onOpenChange={setShowApiKeyModal}
        agent={agent}
        projectId={projectId!}
      />
    </div>
  );
}
