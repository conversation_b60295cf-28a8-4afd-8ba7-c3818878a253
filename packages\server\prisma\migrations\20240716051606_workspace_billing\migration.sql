/*
  Warnings:

  - You are about to drop the column `stripeCustomerId` on the `Workspace` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Workspace" DROP COLUMN "stripeCustomerId";

-- CreateTable
CREATE TABLE "WorkspaceBilling" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "stripeCustomerId" TEXT,
    "FK_workspaceId" TEXT NOT NULL,

    CONSTRAINT "WorkspaceBilling_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "WorkspaceBilling_FK_workspaceId_key" ON "WorkspaceBilling"("FK_workspaceId");

-- Add<PERSON><PERSON>ign<PERSON><PERSON>
ALTER TABLE "WorkspaceBilling" ADD CONSTRAINT "WorkspaceBilling_FK_workspaceId_fkey" FOREIGN KEY ("FK_workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE ON UPDATE CASCADE;
