import { useState } from 'react';

import { Icons } from '../../../components/icons';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Select } from '../../../components/ui/select';
import { Sheet } from '../../../components/ui/sheet';
import { Textarea } from '../../../components/ui/textarea';
import { useToast } from '../../../hooks/useToast';
import { Agent } from '../../../models/agent/agent-model';

interface ApiTestSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agents: Agent[];
}

export function ApiTestSheet({ open, onOpenChange, agents }: ApiTestSheetProps) {
  const { toast } = useToast();
  const [api<PERSON><PERSON>, setApi<PERSON>ey] = useState('');
  const [selectedAgent, setSelectedAgent] = useState('');
  const [message, setMessage] = useState('Hello! Can you help me?');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);

  const handleTest = async () => {
    if (!apiKey.trim()) {
      toast({ title: 'API Key is required', variant: 'destructive' });
      return;
    }
    if (!selectedAgent) {
      toast({ title: 'Please select an agent', variant: 'destructive' });
      return;
    }
    if (!message.trim()) {
      toast({ title: 'Message is required', variant: 'destructive' });
      return;
    }

    setLoading(true);
    setResponse('');

    try {
      const serverUrl = import.meta.env.VITE_SERVER_URL || 'http://localhost:9094';

      // Use the simple chat endpoint (auto-creates task)
      const response = await fetch(`${serverUrl}/api/v1/agents/${selectedAgent}/chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          sessionId: 'test-session',
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorData}`);
      }

      const data = await response.json();
      setResponse(JSON.stringify(data, null, 2));
      toast({ title: 'API test successful!' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setResponse(`Error: ${errorMessage}`);
      toast({ 
        title: 'API test failed', 
        description: errorMessage,
        variant: 'destructive' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopyResponse = async () => {
    if (response) {
      try {
        await navigator.clipboard.writeText(response);
        toast({ title: 'Response copied to clipboard' });
      } catch (error) {
        toast({ title: 'Failed to copy response', variant: 'destructive' });
      }
    }
  };

  const handleCopyCommand = async () => {
    if (!selectedAgent || !apiKey) {
      toast({ title: 'Please fill in agent and API key first', variant: 'destructive' });
      return;
    }

    const serverUrl = import.meta.env.VITE_SERVER_URL || 'http://localhost:9094';
    const command = `# Simple chat endpoint (auto-creates task)
curl -X POST "${serverUrl}/api/v1/agents/${selectedAgent}/chat" \\
  -H "Authorization: Bearer ${apiKey}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "${message.replace(/"/g, '\\"')}",
    "sessionId": "test-session"
  }'`;

    try {
      await navigator.clipboard.writeText(command);
      toast({ title: 'cURL command copied to clipboard' });
    } catch (error) {
      toast({ title: 'Failed to copy command', variant: 'destructive' });
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <Sheet.Content side="right" className="w-[600px] sm:w-[700px] rounded-l-lg border-l">
        <Sheet.Header className="border-b pb-4">
          <Sheet.Title>Test API</Sheet.Title>
          <Sheet.Description>
            Test your agent API endpoints with real requests.
          </Sheet.Description>
        </Sheet.Header>

        <div className="flex-1 overflow-auto py-6 space-y-6">
          {/* API Configuration */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Configuration</h3>
            
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                type="password"
                placeholder="sk-1234567890AB..."
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="agent">Select Agent</Label>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <Select.Trigger>
                  <Select.Value placeholder="Choose an agent to test" />
                </Select.Trigger>
                <Select.Content>
                  {agents.map((agent) => (
                    <Select.Item key={agent.id} value={agent.id}>
                      {agent.name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Test Message</Label>
              <Textarea
                id="message"
                placeholder="Enter your test message..."
                rows={3}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>

            <div className="flex space-x-2">
              <Button 
                onClick={handleTest} 
                disabled={loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Icons.spinner className="w-4 h-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Icons.play className="w-4 h-4 mr-2" />
                    Test API
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleCopyCommand}
                disabled={!selectedAgent || !apiKey}
              >
                <Icons.copy className="w-4 h-4 mr-2" />
                Copy cURL
              </Button>
            </div>
          </div>

          {/* Response */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold">Response</h3>
              {response && (
                <Button variant="outline" size="sm" onClick={handleCopyResponse}>
                  <Icons.copy className="w-4 h-4 mr-2" />
                  Copy
                </Button>
              )}
            </div>
            
            {response ? (
              <div className="border rounded-lg">
                <pre className="text-xs p-4 overflow-auto max-h-96 bg-muted rounded-lg">
                  {response}
                </pre>
              </div>
            ) : (
              <div className="border rounded-lg p-8 text-center text-muted-foreground">
           
                <p>No response yet. Test your API to see results here.</p>
              </div>
            )}
          </div>

          {/* Ready Templates */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Ready Templates</h3>
            <p className="text-xs text-muted-foreground">
              Copy these cURL commands and just paste your API key to test immediately.
            </p>

            <div className="space-y-3">
              {selectedAgent && (
                <>
                  <div className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium">Basic Chat Template</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const serverUrl = import.meta.env.VITE_SERVER_URL || 'http://localhost:9094';
                          const template = `# Step 1: Create a task
curl -X POST "${serverUrl}/api/v1/agents/${selectedAgent}/tasks" \\
  -H "Authorization: Bearer YOUR_API_KEY_HERE" \\
  -H "Content-Type: application/json" \\
  -d '{"name": "API Test Task", "description": "Task created via API"}'

# Step 2: Send message (replace TASK_ID with the id from step 1)
curl -X POST "${serverUrl}/api/v1/agents/${selectedAgent}/tasks/TASK_ID/message" \\
  -H "Authorization: Bearer YOUR_API_KEY_HERE" \\
  -H "Content-Type: application/json" \\
  -d '{"messages": [{"role": "user", "content": "Hello! Can you help me?"}]}'`;
                          navigator.clipboard.writeText(template);
                          toast({ title: 'Template copied! Replace YOUR_API_KEY_HERE and TASK_ID with actual values.' });
                        }}
                      >
                        <Icons.copy className="w-3 h-3 mr-1" />
                        Copy
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p><strong>Agent ID:</strong> {selectedAgent}</p>
                      <p><strong>Message:</strong> "Hello! Can you help me?"</p>
                    </div>
                  </div>

                  <div className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium">Capabilities Template</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const serverUrl = import.meta.env.VITE_SERVER_URL || 'http://localhost:9094';
                          const template = `# Step 1: Create a task
curl -X POST "${serverUrl}/api/v1/agents/${selectedAgent}/tasks" \\
  -H "Authorization: Bearer YOUR_API_KEY_HERE" \\
  -H "Content-Type: application/json" \\
  -d '{"name": "Capabilities Query", "description": "Query agent capabilities"}'

# Step 2: Send message (replace TASK_ID with the id from step 1)
curl -X POST "${serverUrl}/api/v1/agents/${selectedAgent}/tasks/TASK_ID/message" \\
  -H "Authorization: Bearer YOUR_API_KEY_HERE" \\
  -H "Content-Type: application/json" \\
  -d '{"messages": [{"role": "user", "content": "What can you do? Please list your capabilities."}]}'`;
                          navigator.clipboard.writeText(template);
                          toast({ title: 'Template copied! Replace YOUR_API_KEY_HERE and TASK_ID with actual values.' });
                        }}
                      >
                        <Icons.copy className="w-3 h-3 mr-1" />
                        Copy
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p><strong>Agent ID:</strong> {selectedAgent}</p>
                      <p><strong>Message:</strong> "What can you do? Please list your capabilities."</p>
                    </div>
                  </div>

                  <div className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium">Agent Info Template</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const serverUrl = import.meta.env.VITE_SERVER_URL || 'http://localhost:9094';
                          const template = `curl -X GET "${serverUrl}/api/v1/agents/${selectedAgent}" \\
  -H "Authorization: Bearer YOUR_API_KEY_HERE"`;
                          navigator.clipboard.writeText(template);
                          toast({ title: 'Template copied! Replace YOUR_API_KEY_HERE with your actual key.' });
                        }}
                      >
                        <Icons.copy className="w-3 h-3 mr-1" />
                        Copy
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p><strong>Agent ID:</strong> {selectedAgent}</p>
                      <p><strong>Endpoint:</strong> GET /agents/{selectedAgent}</p>
                    </div>
                  </div>
                </>
              )}

              {!selectedAgent && (
                <div className="text-center text-muted-foreground py-4">
                  <p className="text-xs">Select an agent above to see ready templates</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </Sheet.Content>
    </Sheet>
  );
}
