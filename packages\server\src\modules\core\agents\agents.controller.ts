import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { BelongsTo } from '../../../decorators/belongs-to.decorator';
import { Expansion } from '../../../decorators/expansion.decorator';
import { FilterBy } from '../../../decorators/filter-by.decorator';
import { IncludeType } from '../../../decorators/include-type.decorator';
import { User } from '../../../decorators/user.decorator';
import { JwtUser } from '../../../types/jwt-user.type';

import { AgentApiKeysService } from './agent-api-keys.service';
import { AgentsService } from './agents.service';
import { AgentApiKeyExpansionDto } from './dto/agent-api-key-expansion.dto';
import { AgentExpansionDto } from './dto/agent-expansion.dto';
import { AgentFilterByDto } from './dto/agent-filter-by.dto';
import { AgentIncludeTypeDto } from './dto/agent-include-type.dto';
import { CreateAgentApiKeyDto } from './dto/create-agent-api-key.dto';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentApiKeyDto } from './dto/update-agent-api-key.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { UpdateProfileImageDto } from './dto/update-profile-image.dto';

@Controller('projects/:projectId/agents')
@ApiTags('Agents')
@ApiBearerAuth()
export class ProjectAgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  @BelongsTo({ owner: 'either', key: 'projectId', roles: ['MAINTAINER'] })
  async create(
    @Body() data: CreateAgentDto,
    @User() user: JwtUser,
    @Expansion('agents') expansion: AgentExpansionDto,
    @Param('projectId') projectId: string,
  ) {
    const agent = await this.agentsService.create({
      data,
      projectId,
      workspaceId: user.workspaceId,
      expansion,
    });

    return agent;
  }
}

@Controller('agents')
@ApiTags('Agents')
@ApiBearerAuth()
export class AgentsController {
  constructor(
    private readonly agentsService: AgentsService,
    private readonly agentApiKeysService: AgentApiKeysService,
  ) {}

  @Get()
  findAllForWorkspace(
    @User() user: JwtUser,
    @IncludeType('agents') includeType: AgentIncludeTypeDto,
    @Expansion('agents') expansion: AgentExpansionDto,
    @FilterBy('agents') filterBy: AgentFilterByDto,
  ) {
    return this.agentsService.findAllForWorkspace({
      jwtUser: user,
      workspaceId: user.workspaceId,
      includeType,
      expansion,
      filterBy,
    });
  }

  @Get(':agentId')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  async findOne(
    @Param('agentId') agentId: string,
    @Expansion('agents') expansion: AgentExpansionDto,
  ) {
    const agent = await this.agentsService.findOne({
      agentId,
      expansion,
      throwNotFoundException: true,
    });

    return agent;
  }

  @Patch(':agentId')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  async update(
    @Param('agentId') agentId: string,
    @Body() data: UpdateAgentDto,
    @Expansion('agents') expansion: AgentExpansionDto,
  ) {
    const agent = await this.agentsService.update({
      agentId,
      data,
      expansion,
    });

    return agent;
  }

  @Delete(':agentId')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  delete(@Param('agentId') agentId: string) {
    return this.agentsService.delete({
      agentId,
    });
  }

  @Post(':agentId/profile-image-post-url')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  getPresignedPostUrlForProfileImage(
    @Param('agentId') agentId: string,
    @User() user: JwtUser,
    @Body() data: UpdateProfileImageDto,
  ) {
    //This doesn't update the profile image, it just returns a presigned post url to upload a new profile image
    return this.agentsService.getPresignedPostUrlForProfileImage({
      fileName: data.fileName,
      agentId,
      workspaceId: user.workspaceId,
    });
  }

  // API Key Management Endpoints
  @Post(':agentId/api-keys')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  async generateApiKey(
    @Param('agentId') agentId: string,
    @Body() data: CreateAgentApiKeyDto,
    @User() user: JwtUser,
    @Expansion('agentApiKeys') expansion: AgentApiKeyExpansionDto,
  ) {
    return this.agentApiKeysService.generateApiKey({
      agentId,
      workspaceId: user.workspaceId,
      data,
      expansion,
    });
  }

  @Get(':agentId/api-keys')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  async getApiKeys(
    @Param('agentId') agentId: string,
    @User() user: JwtUser,
    @Expansion('agentApiKeys') expansion: AgentApiKeyExpansionDto,
  ) {
    return this.agentApiKeysService.findAllForAgent({
      agentId,
      workspaceId: user.workspaceId,
      expansion,
    });
  }

  @Patch(':agentId/api-keys/:apiKeyId')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  async updateApiKey(
    @Param('agentId') agentId: string,
    @Param('apiKeyId') apiKeyId: string,
    @Body() data: UpdateAgentApiKeyDto,
    @User() user: JwtUser,
    @Expansion('agentApiKeys') expansion: AgentApiKeyExpansionDto,
  ) {
    return this.agentApiKeysService.update({
      apiKeyId,
      workspaceId: user.workspaceId,
      data,
      expansion,
    });
  }

  @Delete(':agentId/api-keys/:apiKeyId')
  @BelongsTo({ owner: 'either', key: 'agentId', roles: ['MAINTAINER'] })
  async deleteApiKey(
    @Param('agentId') agentId: string,
    @Param('apiKeyId') apiKeyId: string,
    @User() user: JwtUser,
  ) {
    return this.agentApiKeysService.delete({
      apiKeyId,
      workspaceId: user.workspaceId,
    });
  }
}
