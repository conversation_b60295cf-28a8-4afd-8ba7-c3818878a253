import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

import { PrismaService } from '../../global/prisma/prisma.service';

import { AgentApiKeyExpansionDto } from './dto/agent-api-key-expansion.dto';
import { CreateAgentApiKeyDto } from './dto/create-agent-api-key.dto';
import { UpdateAgentApiKeyDto } from './dto/update-agent-api-key.dto';

@Injectable()
export class AgentApiKeysService {
  constructor(private prisma: PrismaService) {}

  async generateApiKey({
    agentId,
    workspaceId,
    data,
    expansion,
  }: {
    agentId: string;
    workspaceId: string;
    data: CreateAgentApiKeyDto;
    expansion?: AgentApiKeyExpansionDto;
  }) {
    // Verify agent exists and belongs to workspace
    const agent = await this.prisma.agent.findFirst({
      where: {
        id: agentId,
        project: {
          FK_workspaceId: workspaceId,
        },
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    // Generate a secure API key
    const apiKey = this.#generateSecureApiKey();
    const keyHash = await bcrypt.hash(apiKey, 12);
    const keyPreview = apiKey.substring(0, 5) + '...';

    // Create the API key record
    const newApiKey = await this.prisma.agentApiKey.create({
      data: {
        name: data.name,
        description: data.description,
        keyHash,
        keyPreview,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        permissions: data.permissions || {},
        isActive: true, // Explicitly set to true
        FK_agentId: agentId,
        FK_workspaceId: workspaceId,
      },
      select: {
        id: true,
      },
    });

    // Return the API key with the plain text key (only time it's shown)
    const apiKeyWithDetails = await this.findOne({
      apiKeyId: newApiKey.id,
      expansion,
    });

    return {
      ...apiKeyWithDetails,
      apiKey, // Include the plain text API key only in the response
    };
  }

  async findAllForAgent({
    agentId,
    workspaceId,
    expansion,
  }: {
    agentId: string;
    workspaceId: string;
    expansion?: AgentApiKeyExpansionDto;
  }) {
    // Verify agent exists and belongs to workspace
    const agent = await this.prisma.agent.findFirst({
      where: {
        id: agentId,
        project: {
          FK_workspaceId: workspaceId,
        },
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    const includeClause = this.#buildIncludeClause(expansion);

    return this.prisma.agentApiKey.findMany({
      where: {
        FK_agentId: agentId,
        FK_workspaceId: workspaceId,
      },
      include: includeClause,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async update({
    apiKeyId,
    workspaceId,
    data,
  }: {
    apiKeyId: string;
    workspaceId: string;
    data: { isActive?: boolean; name?: string; description?: string };
  }) {
    // Find and update the API key
    const apiKeyRecord = await this.prisma.agentApiKey.findFirst({
      where: {
        id: apiKeyId,
        FK_workspaceId: workspaceId,
      },
    });

    if (!apiKeyRecord) {
      throw new NotFoundException('API key not found');
    }

    return await this.prisma.agentApiKey.update({
      where: { id: apiKeyId },
      data,
    });
  }

  async delete({
    apiKeyId,
    workspaceId,
  }: {
    apiKeyId: string;
    workspaceId: string;
  }): Promise<void> {
    // Find and delete the API key
    const apiKeyRecord = await this.prisma.agentApiKey.findFirst({
      where: {
        id: apiKeyId,
        FK_workspaceId: workspaceId,
      },
    });

    if (!apiKeyRecord) {
      throw new NotFoundException('API key not found');
    }

    await this.prisma.agentApiKey.delete({
      where: { id: apiKeyId },
    });
  }

  async findOne({
    apiKeyId,
    expansion,
  }: {
    apiKeyId: string;
    expansion?: AgentApiKeyExpansionDto;
  }) {
    const includeClause = this.#buildIncludeClause(expansion);

    const apiKey = await this.prisma.agentApiKey.findUnique({
      where: { id: apiKeyId },
      include: includeClause,
    });

    if (!apiKey) {
      throw new NotFoundException('API key not found');
    }

    return apiKey;
  }

  async update({
    apiKeyId,
    workspaceId,
    data,
    expansion,
  }: {
    apiKeyId: string;
    workspaceId: string;
    data: UpdateAgentApiKeyDto;
    expansion?: AgentApiKeyExpansionDto;
  }) {
    // Verify API key exists and belongs to workspace
    const existingApiKey = await this.prisma.agentApiKey.findFirst({
      where: {
        id: apiKeyId,
        FK_workspaceId: workspaceId,
      },
    });

    if (!existingApiKey) {
      throw new NotFoundException('API key not found');
    }

    await this.prisma.agentApiKey.update({
      where: { id: apiKeyId },
      data: {
        name: data.name,
        description: data.description,
        isActive: data.isActive,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
        permissions: data.permissions,
      },
    });

    return this.findOne({ apiKeyId, expansion });
  }

  async delete({
    apiKeyId,
    workspaceId,
  }: {
    apiKeyId: string;
    workspaceId: string;
  }) {
    // Verify API key exists and belongs to workspace
    const existingApiKey = await this.prisma.agentApiKey.findFirst({
      where: {
        id: apiKeyId,
        FK_workspaceId: workspaceId,
      },
    });

    if (!existingApiKey) {
      throw new NotFoundException('API key not found');
    }

    await this.prisma.agentApiKey.delete({
      where: { id: apiKeyId },
    });

    return true;
  }

  async validateApiKey(apiKey: string) {
    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    console.log('🔑 Validating API key:', apiKey.substring(0, 8) + '...');

    // Find all active API keys and check against the hash
    const apiKeys = await this.prisma.agentApiKey.findMany({
      where: {
        isActive: true,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
      include: {
        agent: {
          include: {
            project: {
              include: {
                workspace: true,
              },
            },
          },
        },
      },
    });

    console.log('🔍 Found', apiKeys.length, 'active API keys in database');

    for (const dbApiKey of apiKeys) {
      console.log('🔐 Checking key:', dbApiKey.keyPreview, 'isActive:', dbApiKey.isActive);
      const isValid = await bcrypt.compare(apiKey, dbApiKey.keyHash);
      console.log('✅ Key match result:', isValid);

      if (isValid) {
        console.log('🎉 API key validated successfully for agent:', dbApiKey.FK_agentId);

        // Update last used timestamp
        await this.prisma.agentApiKey.update({
          where: { id: dbApiKey.id },
          data: { lastUsedAt: new Date() },
        });

        return {
          apiKeyId: dbApiKey.id,
          agentId: dbApiKey.FK_agentId,
          workspaceId: dbApiKey.FK_workspaceId,
          permissions: dbApiKey.permissions,
          agent: dbApiKey.agent,
        };
      }
    }

    console.log('❌ No matching API key found');
    throw new UnauthorizedException('Invalid API key');
  }

  #generateSecureApiKey(): string {
    // Generate a secure random API key like OpenAI format (sk-12 chars)
    const randomPart = crypto.randomBytes(5).toString('hex').toUpperCase(); // 10 chars
    const apiKey = `sk-${randomPart}`;
    return apiKey;
  }

  #buildIncludeClause(expansion?: AgentApiKeyExpansionDto) {
    const include: any = {};

    if (expansion?.expansion?.includes('agent')) {
      include.agent = {
        select: {
          id: true,
          name: true,
          description: true,
        },
      };
    }

    if (expansion?.expansion?.includes('workspace')) {
      include.workspace = {
        select: {
          id: true,
          name: true,
        },
      };
    }

    return include;
  }
}
