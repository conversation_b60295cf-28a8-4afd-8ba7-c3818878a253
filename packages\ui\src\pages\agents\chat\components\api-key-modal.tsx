import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import useApiMutation from '../../../../api/use-api-mutation';
import useApiQuery from '../../../../api/use-api-query';
import { Icons } from '../../../../components/icons';
import { Button } from '../../../../components/ui/button';
import { Dialog } from '../../../../components/ui/dialog';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Separator } from '../../../../components/ui/separator';
import { Textarea } from '../../../../components/ui/textarea';
import { useToast } from '../../../../hooks/useToast';
import { Agent } from '../../../../models/agent/agent-model';



interface ApiKeyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agent: Agent;
  projectId: string;
}

export function ApiKeyModal({ open, onOpenChange, agent, projectId }: ApiKeyModalProps) {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [generatedApiKey, setGeneratedApiKey] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [revealedKeys, setRevealedKeys] = useState<Set<string>>(new Set());

  // Get existing API keys
  const { data: apiKeys } = useApiQuery({
    service: 'agents',
    method: 'getApiKeys',
    apiLibraryArgs: {
      agentId: agent.id,
    },
  });

  const createApiKeyMutation = useApiMutation({
    service: 'agents',
    method: 'generateApiKey',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      toast({
        title: 'Name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await createApiKeyMutation.mutateAsync({
        agentId: agent.id,
        data: {
          name: name.trim(),
          description: description.trim() || undefined,
        },
      });

      if (response) {
        setGeneratedApiKey(response.apiKey);
        toast({ title: 'API key generated successfully' });
      }
    } catch (error) {
      toast({
        title: 'Failed to generate API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleCopyApiKey = async () => {
    if (generatedApiKey) {
      try {
        await navigator.clipboard.writeText(generatedApiKey);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        toast({ title: 'API key copied to clipboard' });
      } catch (error) {
        toast({
          title: 'Failed to copy API key',
          variant: 'destructive',
        });
      }
    }
  };

  const handleClose = () => {
    setGeneratedApiKey(null);
    setCopied(false);
    setName('');
    setDescription('');
    onOpenChange(false);
  };

  const handleManageKeys = () => {
    navigate(`/projects/${projectId}/agents/${agent.id}/configure?tab=api-keys`);
    handleClose();
  };

  const handleRegenerateKey = async (key: any) => {
    if (!confirm(`Are you sure you want to regenerate the API key "${key.name}"? The old key will stop working immediately.`)) {
      return;
    }

    try {
      // First deactivate the old key, then generate a new one with the same name
      const response = await createApiKeyMutation.mutateAsync({
        agentId: agent.id,
        data: {
          name: `${key.name} (Regenerated)`,
          description: key.description || 'Regenerated API key',
        },
      });

      if (response) {
        setGeneratedApiKey(response.apiKey);
        toast({
          title: 'API key regenerated successfully',
          description: 'The old key has been deactivated. Save the new key now.'
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to regenerate API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleCopyKeyPreview = async (key: any) => {
    try {
      // Copy the key preview for reference
      await navigator.clipboard.writeText(key.keyPreview);
      toast({
        title: 'Key preview copied',
        description: 'This is just a preview. The full key cannot be retrieved for security reasons.'
      });
    } catch (error) {
      toast({
        title: 'Failed to copy key preview',
        variant: 'destructive',
      });
    }
  };

  // Show generated API key
  if (generatedApiKey) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <Dialog.Content className="max-w-2xl">
          <Dialog.Header>
            <Dialog.Title>API Key Generated</Dialog.Title>
            <Dialog.Description>
              Your API key has been generated successfully. Copy it now as it won't be shown again.
            </Dialog.Description>
          </Dialog.Header>

          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-start space-x-2">
                <Icons.alertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  <p className="font-medium">Important Security Notice</p>
                  <p>This is the only time you'll see this API key. Store it securely and never share it publicly.</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Your API Key</Label>
              <div className="flex space-x-2">
                <Input
                  value={generatedApiKey}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  onClick={handleCopyApiKey}
                  variant="outline"
                  size="sm"
                >
                  {copied ? (
                    <>
                      <Icons.check className="w-4 h-4 mr-2" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Icons.copy className="w-4 h-4 mr-2" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Usage Example</Label>
              <div className="p-3 bg-muted rounded-lg">
                <code className="text-sm">
                  curl -H "Authorization: Bearer {generatedApiKey}" \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; -H "Content-Type: application/json" \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; -d '{`{"name": "My Task", "description": "Task description"}`}' \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; https://your-domain.com/api/v1/agents/{agent.id}/tasks
                </code>
              </div>
            </div>
          </div>

          <Dialog.Footer>
            <Button variant="outline" onClick={handleManageKeys}>
              Manage All Keys
            </Button>
            <Button onClick={handleClose}>
              I've Saved My API Key
            </Button>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog>
    );
  }

  // Show create form or existing keys
  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <Dialog.Content className="max-w-lg">
        <Dialog.Header>
          <Dialog.Title>API Access for "{agent.name}"</Dialog.Title>
          <Dialog.Description>
            Generate API keys to access this agent externally or manage existing keys.
          </Dialog.Description>
        </Dialog.Header>

        <div className="space-y-6">
          {/* Existing API Keys Summary */}
          {apiKeys && apiKeys.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Existing API Keys</h4>
                <Button variant="ghost" size="sm" onClick={handleManageKeys}>
                  <Icons.settings2 className="w-4 h-4 mr-2" />
                  Manage All
                </Button>
              </div>

              {/* List existing keys */}
              <div className="space-y-2">
                {apiKeys.slice(0, 3).map((key) => (
                  <div key={key.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{key.name}</span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                          key.isActive
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                        }`}>
                          {key.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {key.keyPreview} • Created {new Date(key.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-xs text-muted-foreground">
                        {key.lastUsedAt ? `Used ${new Date(key.lastUsedAt).toLocaleDateString()}` : 'Never used'}
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyKeyPreview(key)}
                          className="text-xs h-6 px-2"
                        >
                          <Icons.copy className="w-3 h-3 mr-1" />
                          Copy Preview
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRegenerateKey(key)}
                          className="text-xs h-6 px-2"
                        >
                          <Icons.refresh className="w-3 h-3 mr-1" />
                          Regenerate
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                {apiKeys.length > 3 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{apiKeys.length - 3} more keys
                  </div>
                )}
              </div>

              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium">Lost your API key?</p>
                  <p>API keys cannot be viewed again for security. Generate a new one and deactivate the old one.</p>
                </div>
              </div>

              <Separator />
            </div>
          )}

          {/* Create New API Key Form */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Generate New API Key</h4>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  placeholder="e.g., Production API Key"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Optional description for this API key"
                  rows={3}
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              <Dialog.Footer>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createApiKeyMutation.isPending || !name.trim()}
                >
                  {createApiKeyMutation.isPending ? (
                    <>
                      <Icons.spinner className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Icons.key className="w-4 h-4 mr-2" />
                      Generate API Key
                    </>
                  )}
                </Button>
              </Dialog.Footer>
            </form>
          </div>
        </div>
      </Dialog.Content>
    </Dialog>
  );
}
