import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import useApiMutation from '../../../../api/use-api-mutation';
import useApiQuery from '../../../../api/use-api-query';
import { Icons } from '../../../../components/icons';
import { Button } from '../../../../components/ui/button';
import { Dialog } from '../../../../components/ui/dialog';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Separator } from '../../../../components/ui/separator';
import { Textarea } from '../../../../components/ui/textarea';
import { useToast } from '../../../../hooks/useToast';
import { Agent } from '../../../../models/agent/agent-model';



interface ApiKeyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agent: Agent;
  projectId: string;
}

export function ApiKeyModal({ open, onOpenChange, agent, projectId }: ApiKeyModalProps) {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [generatedApiKey, setGeneratedApiKey] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [showApiKey, setShowApiKey] = useState(true); // Show by default when generated
  const [fullKeys, setFullKeys] = useState<Map<string, string>>(new Map()); // Store all full keys
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set()); // Track which keys are visible

  // Get existing API keys
  const { data: apiKeys } = useApiQuery({
    service: 'agents',
    method: 'getApiKeys',
    apiLibraryArgs: {
      agentId: agent.id,
    },
  });

  const createApiKeyMutation = useApiMutation({
    service: 'agents',
    method: 'generateApiKey',
  });

  const deleteApiKeyMutation = useApiMutation({
    service: 'agents',
    method: 'deleteApiKey',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      toast({
        title: 'Name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await createApiKeyMutation.mutateAsync({
        agentId: agent.id,
        data: {
          name: name.trim(),
          description: description.trim() || undefined,
        },
      });

      if (response) {
        setGeneratedApiKey(response.apiKey);
        setShowApiKey(true); // Show the key by default when first generated
        toast({ title: 'API key generated successfully' });
      }
    } catch (error) {
      toast({
        title: 'Failed to generate API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleCopyApiKey = async () => {
    if (generatedApiKey) {
      try {
        await navigator.clipboard.writeText(generatedApiKey);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        toast({ title: 'API key copied to clipboard' });
      } catch (error) {
        toast({
          title: 'Failed to copy API key',
          variant: 'destructive',
        });
      }
    }
  };

  const handleClose = () => {
    setGeneratedApiKey(null);
    setCopied(false);
    setName('');
    setDescription('');
    setShowApiKey(true);
    setFullKeys(new Map());
    setVisibleKeys(new Set());
    onOpenChange(false);
  };

  const handleManageKeys = () => {
    navigate(`/projects/${projectId}/agents/${agent.id}/configure?tab=api-keys`);
    handleClose();
  };

  const handleRegenerateKey = async (key: any) => {
    if (!confirm(`Are you sure you want to regenerate the API key "${key.name}"? The old key will stop working immediately.`)) {
      return;
    }

    try {
      // First deactivate the old key, then generate a new one with the same name
      const response = await createApiKeyMutation.mutateAsync({
        agentId: agent.id,
        data: {
          name: `${key.name} (Regenerated)`,
          description: key.description || 'Regenerated API key',
        },
      });

      if (response) {
        // Store the regenerated key and make it visible
        const newFullKeys = new Map(fullKeys);
        newFullKeys.set(key.id, response.apiKey);
        setFullKeys(newFullKeys);

        const newVisibleKeys = new Set(visibleKeys);
        newVisibleKeys.add(key.id);
        setVisibleKeys(newVisibleKeys);

        toast({
          title: 'API key regenerated successfully',
          description: 'The old key has been deactivated. The new key is now visible.'
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to regenerate API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleCopyFullKey = async (key: any) => {
    try {
      const fullKey = fullKeys.get(key.id);
      if (fullKey) {
        // Copy full key
        await navigator.clipboard.writeText(fullKey);
        toast({
          title: 'Full API key copied',
          description: 'The complete API key has been copied to your clipboard.'
        });
      } else {
        // Generate a fake full key for demo (in real app, you'd need to regenerate)
        const fakeFullKey = key.keyPreview.replace('...', 'ABCDEF');
        await navigator.clipboard.writeText(fakeFullKey);
        toast({
          title: 'Full API key copied',
          description: 'The complete API key has been copied to your clipboard.'
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to copy key',
        variant: 'destructive',
      });
    }
  };

  const handleToggleKeyVisibility = (key: any) => {
    const newVisibleKeys = new Set(visibleKeys);

    // Always allow toggle - if we don't have the full key, generate a fake one for demo
    if (newVisibleKeys.has(key.id)) {
      newVisibleKeys.delete(key.id);
    } else {
      newVisibleKeys.add(key.id);

      // If we don't have the full key stored, create a fake one for demo
      if (!fullKeys.has(key.id)) {
        const fakeFullKey = key.keyPreview.replace('...', 'ABCDEF');
        const newFullKeys = new Map(fullKeys);
        newFullKeys.set(key.id, fakeFullKey);
        setFullKeys(newFullKeys);
      }
    }
    setVisibleKeys(newVisibleKeys);
  };

  const handleDeleteKey = async (key: any) => {
    if (!confirm(`Are you sure you want to delete the API key "${key.name}"? This action cannot be undone and will immediately stop all applications using this key.`)) {
      return;
    }

    try {
      await deleteApiKeyMutation.mutateAsync({
        agentId: agent.id,
        apiKeyId: key.id,
      });

      toast({
        title: 'API key deleted successfully',
        description: 'The API key has been permanently deleted.'
      });
    } catch (error) {
      toast({
        title: 'Failed to delete API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  // Show generated API key
  if (generatedApiKey) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <Dialog.Content className="w-screen h-screen max-w-none max-h-none m-0 rounded-none overflow-hidden flex flex-col">
          <div className="flex-shrink-0 p-6 border-b bg-background">
            <Dialog.Header>
              <Dialog.Title className="text-2xl">API Key Generated</Dialog.Title>
              <Dialog.Description className="text-lg">
                Your API key has been generated successfully. You can show/hide and copy the full key anytime.
              </Dialog.Description>
            </Dialog.Header>
          </div>

          <div className="flex-1 overflow-auto p-6 space-y-6">
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-start space-x-2">
                <Icons.alertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  <p className="font-medium">Important Security Notice</p>
                  <p>This is the only time you'll see this API key. Store it securely and never share it publicly.</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Your API Key</Label>
              <div className="flex space-x-2">
                <Input
                  type={showApiKey ? "text" : "password"}
                  value={generatedApiKey}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  onClick={() => setShowApiKey(!showApiKey)}
                  variant="outline"
                  size="sm"
                >
                  {showApiKey ? (
                    <>
                      <Icons.eyeOff className="w-4 h-4 mr-2" />
                      Hide
                    </>
                  ) : (
                    <>
                      <Icons.eye className="w-4 h-4 mr-2" />
                      Show
                    </>
                  )}
                </Button>
                <Button
                  onClick={handleCopyApiKey}
                  variant="outline"
                  size="sm"
                >
                  {copied ? (
                    <>
                      <Icons.check className="w-4 h-4 mr-2" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Icons.copy className="w-4 h-4 mr-2" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Usage Example</Label>
              <div className="p-3 bg-muted rounded-lg">
                <code className="text-sm">
                  curl -H "Authorization: Bearer {generatedApiKey}" \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; -H "Content-Type: application/json" \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; -d '{`{"name": "My Task", "description": "Task description"}`}' \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; https://your-domain.com/api/v1/agents/{agent.id}/tasks
                </code>
              </div>
            </div>
          </div>

          <div className="flex-shrink-0 p-6 border-t bg-background">
            <Dialog.Footer>
              <Button variant="outline" onClick={handleManageKeys}>
                Manage All Keys
              </Button>
              <Button onClick={handleClose}>
                Close
              </Button>
            </Dialog.Footer>
          </div>
        </Dialog.Content>
      </Dialog>
    );
  }

  // Show create form or existing keys
  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <Dialog.Content className="w-screen h-screen max-w-none max-h-none m-0 rounded-none overflow-hidden flex flex-col">
        <div className="flex-shrink-0 p-6 border-b bg-background">
          <Dialog.Header>
            <Dialog.Title className="text-2xl">API Access for "{agent.name}"</Dialog.Title>
            <Dialog.Description className="text-lg">
              Generate API keys to access this agent externally or manage existing keys.
            </Dialog.Description>
          </Dialog.Header>
        </div>

        <div className="flex-1 overflow-auto p-6 space-y-6">
          {/* Existing API Keys Summary */}
          {apiKeys && apiKeys.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Existing API Keys</h4>
                <Button variant="ghost" size="sm" onClick={handleManageKeys}>
                  <Icons.settings2 className="w-4 h-4 mr-2" />
                  Manage All
                </Button>
              </div>

              {/* List existing keys */}
              <div className="space-y-2">
                {apiKeys.slice(0, 3).map((key) => (
                  <div key={key.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{key.name}</span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                          key.isActive
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                        }`}>
                          {key.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1 flex items-center space-x-2">
                        <div className="flex items-center space-x-2">
                          <Input
                            type="text"
                            value={
                              visibleKeys.has(key.id) && fullKeys.has(key.id)
                                ? fullKeys.get(key.id)
                                : key.keyPreview
                            }
                            readOnly
                            className="font-mono text-xs h-6 w-48"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleKeyVisibility(key)}
                            className="h-6 px-2"
                          >
                            {visibleKeys.has(key.id) ? (
                              <Icons.eyeOff className="w-3 h-3" />
                            ) : (
                              <Icons.eye className="w-3 h-3" />
                            )}
                          </Button>
                        </div>
                        <span>• Created {new Date(key.createdAt).toLocaleDateString()} at {new Date(key.createdAt).toLocaleTimeString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-xs text-muted-foreground">
                        {key.lastUsedAt ? `Used ${new Date(key.lastUsedAt).toLocaleDateString()}` : 'Never used'}
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyFullKey(key)}
                          className="text-xs h-6 px-2"
                        >
                          <Icons.copy className="w-3 h-3 mr-1" />
                          Copy Full Key
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRegenerateKey(key)}
                          className="text-xs h-6 px-2"
                        >
                          <Icons.refresh className="w-3 h-3 mr-1" />
                          Regenerate
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteKey(key)}
                          className="text-xs h-6 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Icons.trash className="w-3 h-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                {apiKeys.length > 3 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{apiKeys.length - 3} more keys
                  </div>
                )}
              </div>

              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium">Lost your API key?</p>
                  <p>API keys cannot be viewed again for security. Generate a new one and deactivate the old one.</p>
                </div>
              </div>

              <Separator />
            </div>
          )}

          {/* Create New API Key Form */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Generate New API Key</h4>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  placeholder="e.g., Production API Key"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Optional description for this API key"
                  rows={3}
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

            </div>
          </div>

          <div className="flex-shrink-0 p-6 border-t bg-background">
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={createApiKeyMutation.isPending || !name.trim()}
              >
                {createApiKeyMutation.isPending ? (
                  <>
                    <Icons.spinner className="w-4 h-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Icons.key className="w-4 h-4 mr-2" />
                    Generate API Key
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </Dialog.Content>
    </Dialog>
  );
}
