{"name": "lecca-io", "version": "0.47.0", "license": "public", "repository": {"type": "git", "url": "https://github.com/lecca-digital/lecca-io"}, "scripts": {"vercel-build": "npx prisma generate && nx build server", "postinstall": "prisma generate", "start": "node dist/packages/server/main.js", "start:server": "nx serve server", "start:ui": "nx serve ui", "watch:toolkit": "nx build toolkit --watch", "watch:apps": "nx build apps --watch", "start:tunnel": "dotenv -- cross-var ngrok http --domain=tunnel.lecca.io %PORT%", "start:stripe": "dotenv -- cross-var stripe listen --forward-to %SERVER_URL%/webhooks/stripe"}, "private": false, "prepare": "husky install", "dependencies": {"@ai-sdk/anthropic": "^1.0.2", "@ai-sdk/deepseek": "^0.1.2", "@ai-sdk/google": "^1.0.3", "@ai-sdk/openai": "^1.1.9", "@ai-sdk/togetherai": "^0.1.0", "@ai-sdk/xai": "^1.1.6", "@anatine/zod-mock": "^3.13.3", "@aws-sdk/client-s3": "^3.699.0", "@aws-sdk/client-ssm": "^3.699.0", "@aws-sdk/s3-presigned-post": "^3.699.0", "@aws-sdk/s3-request-presigner": "^3.699.0", "@codesandbox/sdk": "^0.1.0", "@commitlint/cli": "19.6.0", "@commitlint/config-conventional": "19.6.0", "@dagrejs/dagre": "^1.1.4", "@faker-js/faker": "^8.4.1", "@hookform/resolvers": "^3.3.4", "@lecca-io/apps": "0.47.0", "@lecca-io/toolkit": "0.47.0", "@nestjs/axios": "^3.1.2", "@nestjs/common": "^10.4.8", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.8", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.6", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.2", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^8.0.7", "@nestjs/throttler": "^6.2.1", "@nestjs/websockets": "^11.1.0", "@notionhq/client": "^2.3.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.55.2", "@opentelemetry/exporter-trace-otlp-http": "^0.57.0", "@opentelemetry/resources": "^1.30.0", "@opentelemetry/sdk-node": "^0.57.0", "@pinecone-database/pinecone": "^4.0.0", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-portal": "^1.1.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.51.15", "@tanstack/react-query-devtools": "^5.51.15", "@tanstack/react-table": "^8.13.2", "@tiptap/core": "^2.2.4", "@tiptap/extension-placeholder": "^2.2.4", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@types/react-syntax-highlighter": "^15.5.13", "@vapi-ai/web": "^2.1.7", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.0.10", "ai": "^4.1.35", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "canvas-confetti": "^1.9.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.0", "cmdk": "^0.2.1", "cron": "^3.2.1", "d3-hierarchy": "^3.1.2", "d3-timer": "^3.0.1", "date-fns": "^3.4.0", "dotenv": "^16.4.5", "embla-carousel": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.0.2", "express": "^4.21.1", "framer-motion": "^11.11.11", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0", "input-otp": "^1.2.4", "jsonrepair": "^3.10.0", "jszip": "^3.10.1", "luxon": "^3.5.0", "mammoth": "^1.8.0", "mathjs": "^14.0.0", "nodemailer": "^6.9.16", "ollama-ai-provider": "^1.0.0", "openai": "^4.73.0", "passport-jwt": "^4.0.1", "raw-body": "^3.0.0", "react": "18.3.1", "react-day-picker": "^8.10.0", "react-dom": "18.3.1", "react-dropzone": "^14.2.3", "react-dropzone-uploader": "^2.11.0", "react-hook-form": "^7.51.0", "react-image-file-resizer": "^0.4.8", "react-json-tree": "^0.19.0", "react-markdown": "9.0.1", "react-pdftotext": "^1.3.0", "react-resizable-panels": "^2.0.12", "react-router-dom": "^6.22.3", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.10.4", "recharts": "^2.15.3", "reflect-metadata": "^0.2.2", "rrule": "^2.8.1", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^17.4.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^0.9.0", "vercel": "^33.6.2", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "19.6.0", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "^9.8.0", "@nestjs/cli": "^10.4.8", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.8", "@nx/devkit": "20.1.3", "@nx/eslint": "20.1.3", "@nx/eslint-plugin": "20.1.3", "@nx/jest": "20.1.3", "@nx/js": "20.1.3", "@nx/nest": "^20.1.3", "@nx/node": "20.1.3", "@nx/playwright": "20.1.3", "@nx/react": "20.1.3", "@nx/vite": "20.1.3", "@nx/web": "20.1.3", "@nx/webpack": "20.1.3", "@nx/workspace": "20.1.3", "@playwright/test": "^1.36.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/canvas-confetti": "^1.6.4", "@types/d3-hierarchy": "^3.1.6", "@types/d3-timer": "^3.0.2", "@types/express": "^5.0.0", "@types/express-serve-static-core": "^5.0.2", "@types/jest": "^29.5.12", "@types/luxon": "^3.4.2", "@types/node": "^22.9.3", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/qs": "^6.9.17", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/socket.io": "^3.0.2", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.2.0", "@vitejs/plugin-react-swc": "^3.6.0", "@vitest/ui": "^1.3.1", "autoprefixer": "10.4.13", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "cross-var": "^1.1.0", "cz-conventional-changelog": "^3.3.0", "dotenv-cli": "^7.4.4", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jsdom": "~22.1.0", "lint-staged": "^15.2.2", "nx": "20.1.3", "postcss": "8.4.38", "prettier": "^3.3.3", "prisma": "^5.22.0", "semantic-release": "^24.2.0", "tailwindcss": "3.4.3", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tslib": "^2.3.0", "typescript": "^5.6.3", "typescript-eslint": "^8.0.0", "verdaccio": "^5.0.4", "vite": "^5.1.6", "vitest": "^1.3.1", "webpack-cli": "^5.1.4"}, "prisma": {"schema": "./packages/server/prisma/schema.prisma"}, "nx": {"includedScripts": []}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@parcel/watcher", "@prisma/client", "@prisma/engines", "@scarf/scarf", "@swc/core", "@vercel/speed-insights", "bcrypt", "canvas", "core-js", "esbuild", "nx", "prisma", "protobufjs"]}}