import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AgentApiKeysService } from '../modules/core/agents/agent-api-keys.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private agentApiKeysService: AgentApiKeysService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = this.extractApiKey(request);

    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    try {
      const apiKeyContext = await this.agentApiKeysService.validateApiKey(apiKey);

      // Add user context to request
      request.user = {
        email: 'api-key-user',
        userId: 'api-key-user',
        workspaceId: apiKeyContext.workspaceId,
        workspaceUserId: 'api-key-user',
        roles: ['MAINTAINER'],
        apiKeyId: apiKeyContext.apiKeyId,
        agentId: apiKeyContext.agentId,
        permissions: apiKeyContext.permissions as Record<string, any>,
        isApiKeyAuth: true,
      };

      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid API key');
    }
  }

  private extractApiKey(request: any): string | null {
    // Check for API key in different locations
    // 1. Authorization header with Bearer token
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 2. X-API-Key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // 3. Query parameter
    const apiKeyQuery = request.query.api_key;
    if (apiKeyQuery) {
      return apiKeyQuery;
    }

    return null;
  }
}
